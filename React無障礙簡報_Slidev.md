---
theme: default
background: https://source.unsplash.com/1920x1080/?technology,accessibility
class: text-center
highlighter: shiki
lineNumbers: false
info: |
  ## React 無障礙開發實戰指南
  基於 WCAG 2.1 指南的完整實作指南，包含 Freego 檢測工具設定
drawings:
  persist: false
transition: slide-left
title: React 無障礙開發實戰指南
mdc: true
---

# React 無障礙網站開發

## 從程式碼角度解決無障礙問題

<div class="pt-12">
  <span @click="$slidev.nav.next" class="px-2 py-1 rounded cursor-pointer" hover="bg-white bg-opacity-10">
    基於 WCAG 2.1 指南 <carbon:arrow-right class="inline"/>
  </span>
</div>

<div class="abs-br m-6 flex gap-2">
  <button @click="$slidev.nav.openInEditor()" title="Open in Editor" class="text-xl slidev-icon-btn opacity-50 !border-none !hover:text-white">
    <carbon:edit />
  </button>
</div>

<style>
h1 {
  background-color: #2B90B6;
  background-image: linear-gradient(45deg, #4EC5D4 10%, #146b8c 20%);
  background-size: 100%;
  -webkit-background-clip: text;
  -moz-background-clip: text;
  -webkit-text-fill-color: transparent;
  -moz-text-fill-color: transparent;
}
</style>

---

# 🤔 你是否遇過這些情況？

<v-clicks>

- PM 說：「我們需要通過無障礙檢測」
- QA 回報：「鍵盤無法操作某些功能」
- 使用者抱怨：「螢幕報讀器讀不出內容」
- 設計師問：「這個顏色對比度夠嗎？」
- 法務提醒：「我們可能面臨無障礙法規風險」

</v-clicks>

<br>

<v-click>

## **今天我們來解決這些問題！** 🚀

</v-click>

---

# 🎯 為什麼 React 工程師需要關心無障礙？

<div grid="~ cols-2 gap-4">
<div>

## 📊 數據說話

- 全球 15% 人口有身心障礙
- 台灣約 120 萬身心障礙者
- 每個人都可能暫時性障礙

## ⚖️ 法規要求

- 政府網站強制要求
- 企業 ESG 評估項目
- 國際市場准入條件

</div>
<div>

## 💰 商業價值

- 擴大使用者群體
- 改善 SEO 排名
- 提升品牌形象

## 🛠️ 技術品質

- 更好的程式碼結構
- 更清晰的語義
- 更穩健的元件

</div>
</div>

---

# WCAG 2.1 四大原則 (POUR)

<div grid="~ cols-2 gap-4">
<div>

## 👁️ Perceivable 可感知

使用者必須能夠感知資訊
<small>替代文字、字幕、顏色對比</small>

## ⌨️ Operable 可操作

介面元件必須是可操作的
<small>鍵盤導覽、無癲癇風險</small>

</div>
<div>

## 🧠 Understandable 可理解

資訊和操作必須是可理解的
<small>清晰文字、一致行為</small>

## 🔧 Robust 穩健

內容必須能被各種輔助科技解讀
<small>有效 HTML、ARIA 支援</small>

</div>
</div>

---

# WCAG 檢測等級

<div grid="~ cols-3 gap-4">
<div class="border rounded p-4">

## 🥉 Level A

**基礎**
最低要求，必須滿足
<small>基本的無障礙功能</small>

</div>
<div class="border rounded p-4 bg-yellow-50">

## 🥈 Level AA

**標準** ← **建議目標**
大多數法規要求的等級

</div>
<div class="border rounded p-4">

## 🥇 Level AAA

**最高**
最嚴格要求
<small>通常不要求全站達到</small>

</div>
</div>

<br>

<div class="text-center">
💡 <strong>建議：以 AA 為目標，A 為底線</strong>
</div>

---

# 🏷️ ARIA 簡介

## 什麼是 ARIA？

**ARIA (Accessible Rich Internet Applications)** 是一套屬性，用來增強 HTML 的語義，讓輔助科技能更好地理解網頁內容。

<div grid="~ cols-2 gap-4">
<div>

## 🎯 ARIA 的三大支柱

### 1. **Roles (角色)**

定義元素的用途

```jsx
<div role="button">按鈕</div>
<div role="alert">警告訊息</div>
```

### 2. **Properties (屬性)**

描述元素的特性

```jsx
<input aria-label="搜尋" />
<div aria-describedby="help-text">內容</div>
```

</div>
<div>

### 3. **States (狀態)**

描述元素的當前狀態

```jsx
<button aria-pressed="true">已按下</button>
<div aria-expanded="false">摺疊狀態</div>
```

## 💡 ARIA 的黃金法則

**「能用原生 HTML 就不要用 ARIA」**

```jsx
// ❌ 不必要的 ARIA
<div role="button" tabIndex="0">按鈕</div>

// ✅ 使用原生元素
<button>按鈕</button>
```

</div>
</div>

---

# 🏷️ aria-label 屬性

## 為元素提供可訪問的名稱

當視覺標籤不足或不存在時，`aria-label` 提供螢幕報讀器可以讀出的名稱。

<div grid="~ cols-2 gap-4">
<div>

## ❌ 常見問題

```jsx
// 圖示按鈕沒有文字說明
<button>
  <SearchIcon />
</button>

// 輸入欄位只有 placeholder
<input placeholder="請輸入關鍵字" />

// 連結文字不明確
<a href="/download">點擊這裡</a>

// 關閉按鈕只有符號
<button>×</button>
```

</div>
<div>

## ✅ React 解決方案

```jsx
// 為圖示按鈕提供明確說明
<button aria-label="搜尋產品">
  <SearchIcon aria-hidden="true" />
</button>

// 輸入欄位有明確標籤
<input
  aria-label="搜尋關鍵字"
  placeholder="請輸入關鍵字"
/>

// 連結文字描述目的
<a href="/download" aria-label="下載產品手冊 PDF">
  點擊這裡
</a>

// 關閉按鈕有明確說明
<button aria-label="關閉對話框">×</button>
```

</div>
</div>

---

# 🔗 aria-labelledby 屬性

## 使用其他元素作為標籤

`aria-labelledby` 讓你可以引用頁面上其他元素的文字作為當前元素的標籤。

<div grid="~ cols-2 gap-4">
<div>

## 🎯 使用時機

- 標籤文字已存在於頁面其他地方
- 需要組合多個元素的文字作為標籤
- 標題和內容需要建立關聯

## 基本語法

```jsx
// 引用單一元素
<h2 id="billing">帳單地址</h2>
<fieldset aria-labelledby="billing">
  <input type="text" placeholder="街道地址" />
</fieldset>

// 引用多個元素
<span id="pwd">密碼</span>
<span id="strength">強度：強</span>
<input
  type="password"
  aria-labelledby="pwd strength"
/>
```

</div>
<div>

## ✅ React 實際應用

```jsx
// 表單區塊標籤
const AddressForm = () => (
  <div>
    <h3 id="shipping-title">配送地址</h3>
    <fieldset aria-labelledby="shipping-title">
      <input placeholder="收件人姓名" />
      <input placeholder="聯絡電話" />
      <textarea placeholder="詳細地址"></textarea>
    </fieldset>
  </div>
);

// 統計卡片
const StatCard = ({ title, value, trend }) => (
  <div aria-labelledby={`${title}-label ${title}-value`}>
    <h4 id={`${title}-label`}>{title}</h4>
    <span id={`${title}-value`}>{value}</span>
    <span>{trend}</span>
  </div>
);

// 對話框標題
const Modal = ({ titleId, children }) => (
  <div role="dialog" aria-labelledby={titleId}>
    <h2 id={titleId}>對話框標題</h2>
    {children}
  </div>
);
```

</div>
</div>

---

# 🙈 aria-hidden 屬性

## 隱藏裝飾性內容

`aria-hidden` 告訴輔助科技忽略某些元素，通常用於純裝飾性的內容。

<div grid="~ cols-2 gap-4">
<div>

## 🎯 使用時機

- **裝飾性圖示**：不傳達重要資訊的圖示
- **重複內容**：已有文字說明的視覺元素
- **純視覺效果**：分隔線、背景圖案等
- **暫時隱藏**：動態顯示/隱藏的內容

## ⚠️ 注意事項

```jsx
// ❌ 不要隱藏重要內容
<button aria-hidden="true">
  重要按鈕 {/* 這會讓按鈕無法被存取 */}
</button>

// ❌ 不要隱藏可聚焦的元素
<input aria-hidden="true" /> {/* 仍可用 Tab 聚焦 */}
```

</div>
<div>

## ✅ React 正確使用

```jsx
// 裝飾性圖示
<button aria-label="儲存檔案">
  <SaveIcon aria-hidden="true" />
  儲存
</button>

// 純視覺分隔線
<div className="section">
  <h2>第一節</h2>
  <hr aria-hidden="true" />
  <p>內容...</p>
</div>

// 重複的視覺提示
<div className="status-success">
  <CheckIcon aria-hidden="true" />
  <span>成功：資料已儲存</span>
</div>

// 條件式隱藏
const Notification = ({ show, message }) => (
  <div
    className={`notification ${show ? 'visible' : 'hidden'}`}
    aria-hidden={!show}
    role={show ? 'alert' : undefined}
  >
    {message}
  </div>
);
```

</div>
</div>

---

# ⚠️ 使用 ARIA 的注意事項

## 避免常見錯誤，確保無障礙品質

<div grid="~ cols-2 gap-4">
<div>

## 🚫 不要濫用 Role

### ❌ 錯誤做法

```jsx
// 原生元素已有正確語義
<button role="button">按鈕</button>
<input role="textbox" type="text" />
<a role="link" href="/page">連結</a>

// 使用錯誤的 role
<div role="button">
  <a href="/page">連結</a> {/* 巢狀互動元素 */}
</div>
```

### ✅ 正確做法

```jsx
// 直接使用原生元素
<button>按鈕</button>
<input type="text" />
<a href="/page">連結</a>

// 只在必要時使用 role
<div
  role="button"
  tabIndex="0"
  onKeyDown={handleKeyDown}
  onClick={handleClick}
>
  自定義按鈕
</div>
```

</div>
<div>

## 🎯 正確管理 tabindex

### ❌ 錯誤做法

```jsx
// 破壞自然的 Tab 順序
<button tabIndex="1">第一個</button>
<input tabIndex="3" />
<button tabIndex="2">第二個</button>

// 讓不該聚焦的元素可聚焦
<div tabIndex="0">裝飾性內容</div>
```

### ✅ 正確做法

```jsx
// 使用 0 或 -1
<div
  role="button"
  tabIndex="0"  // 加入 Tab 順序
  onClick={handleClick}
>
  自定義按鈕
</div>

<div
  role="option"
  tabIndex="-1"  // 可程式化聚焦，但不在 Tab 順序中
>
  選項
</div>
```

</div>
</div>

---

# ⚠️ 使用 ARIA 的注意事項 (續)

<div grid="~ cols-2 gap-4">
<div>

## 🏷️ 確保可訪問名稱

### ❌ 錯誤做法

```jsx
// 沒有可訪問名稱
<button></button>
<input type="text" />

// 名稱不一致
<button aria-label="關閉">確定</button>

// 空的標籤
<button aria-label="">按鈕</button>
```

### ✅ 正確做法

```jsx
// 使用文字內容
<button>儲存</button>

// 使用 aria-label
<button aria-label="關閉對話框">×</button>

// 使用 aria-labelledby
<h2 id="form-title">聯絡表單</h2>
<form aria-labelledby="form-title">
  <input aria-label="姓名" />
</form>
```

</div>
<div>

## 🔄 保持狀態同步

### ❌ 錯誤做法

```jsx
// 狀態不同步
const [expanded, setExpanded] = useState(false);
return (
  <button
    aria-expanded="false"  // 永遠是 false
    onClick={() => setExpanded(!expanded)}
  >
    展開選單
  </button>
);
```

### ✅ 正確做法

```jsx
// 狀態正確同步
const [expanded, setExpanded] = useState(false);
return (
  <button
    aria-expanded={expanded}
    onClick={() => setExpanded(!expanded)}
  >
    {expanded ? '收合' : '展開'}選單
  </button>
);
```

</div>
</div>

---

# 指引 1.1 - 替代文字 (Level A)

為所有非文字內容提供等義的文字替代

<div grid="~ cols-2 gap-4">
<div>

## ❌ 常見問題

```jsx
// 缺少 alt 屬性
<img src="sales-chart.png" />

// 無意義的 alt
<img src="chart.png" alt="圖片" />

// 圖示按鈕無法理解用途
<button><SearchIcon /></button>

// 裝飾圖片有不必要的 alt
<img src="decoration.svg" alt="裝飾圖案" />
```

</div>
<div>

## ✅ React 解決方案

```jsx
// 資訊性圖片：描述內容和意義
<img src="sales-chart.png"
     alt="2023年第三季銷售成長20%趨勢圖" />

// 功能性圖示：描述功能而非外觀
<button aria-label="搜尋產品">
  <SearchIcon aria-hidden="true" />
</button>

// 裝飾性圖片：空 alt 讓螢幕報讀器忽略
<img src="decoration.svg" alt="" />
```

</div>
</div>

---

# 指引 1.2 - 時序媒體 (Level A/AA)

為時序媒體提供替代方案

<div grid="~ cols-2 gap-4">
<div>

## ❌ 常見問題

```jsx
// 影片沒有字幕
<video controls>
  <source src="tutorial.mp4" type="video/mp4" />
</video>

// 音訊沒有文字替代
<audio controls>
  <source src="podcast.mp3" type="audio/mpeg" />
</audio>
```

</div>
<div>

## ✅ React 解決方案

```jsx
// 提供字幕和文字稿
const VideoPlayer = ({ src, captionsSrc }) => (
  <div>
    <video controls>
      <source src={src} type="video/mp4" />
      <track kind="captions"
             src={captionsSrc}
             srcLang="zh-TW"
             label="繁體中文字幕"
             default />
    </video>
    <details>
      <summary>影片文字稿</summary>
      <p>完整的影片內容文字描述...</p>
    </details>
  </div>
);
```

</div>
</div>

---

# 指引 1.3 - 可調適 (Level A/AA)

建立能以不同方式呈現而不失去意義的內容

<div class="bg-yellow-100 border-l-4 border-yellow-500 p-3 mb-3 text-sm">
⚠️ <strong>檢測發現問題</strong><br>
<code>HM1130100C</code> 標頭組件未按照正確的巢狀層次結構配置<br>
<strong>影響指引：</strong> 1.3.1 資訊與關連性 (Level A)
</div>

<div grid="~ cols-2 gap-3">
<div>

## ❌ 常見問題

```jsx
// 全部用 div，沒有語義
const BadLayout = () => (
  <div>
    <div className="title">網站標題</div>
    <div className="menu">
      <div>首頁</div>
      <div>關於我們</div>
    </div>
    <div className="content">主要內容</div>
  </div>
);

// 表單沒有正確關聯
<div>
  <span>姓名</span>
  <input type="text" />
</div>
```

</div>
<div>

## ✅ React 解決方案

```jsx
// 使用正確的 HTML 語義標籤
const GoodLayout = () => (
  <div>
    <header>
      <h1>網站標題</h1>
      <nav aria-label="主要導覽">
        <ul>
          <li><a href="/">首頁</a></li>
          <li><a href="/about">關於我們</a></li>
        </ul>
      </nav>
    </header>
    <main>
      <h2>主要內容標題</h2>
      <p>內容段落...</p>
    </main>
  </div>
);

// 正確的表單關聯
<div>
  <label htmlFor="name">姓名</label>
  <input id="name" type="text" />
</div>
```

</div>
</div>

---

# 指引 1.4 - 可辨識 (Level A/AA)

讓使用者更容易看見及聽到內容

<div class="bg-yellow-100 border-l-4 border-yellow-500 p-3 mb-3 text-sm">
⚠️ <strong>檢測發現問題</strong><br>
<code>CS2140401C</code> CSS 樣式未使用相對字型尺寸單位
</div>

<div grid="~ cols-2 gap-3">
<div>

## ❌ 常見問題

```jsx
// 僅用顏色傳達資訊
const BadStatus = ({ type, message }) => (
  <div className={`status-${type}`}>
    {message}
  </div>
);

// 對比度不足、固定字體大小
.low-contrast {
  color: #999999;
  font-size: 14px; /* 無法縮放 */
}
```

</div>
<div>

## ✅ React 解決方案

```jsx
// 不僅依賴顏色，加上圖示和文字
const GoodStatus = ({ type, message }) => (
  <div className={`status-${type}`}>
    {type === 'error' && <ErrorIcon />}
    {type === 'success' && <CheckIcon />}
    <span>{type === 'error' ? '錯誤：' : '成功：'}
          {message}</span>
  </div>
);

// 足夠對比度、相對單位
.good-contrast {
  color: #212529;
  font-size: 1rem; /* 可縮放 */
}
```

</div>
</div>

---

# 指引 2.1 - 鍵盤可操作 (Level A)

讓所有功能都能透過鍵盤使用

<div grid="~ cols-2 gap-4">
<div>

### ❌ 常見問題

```jsx
// 只能用滑鼠操作的元件
<div onClick={handleClick}>
  點擊我
</div>

// 自定義元件沒有鍵盤支援
const CustomButton = ({ onClick }) => (
  <span className="button" onClick={onClick}>
    按鈕
  </span>
);

// 焦點陷阱
<div tabIndex={0}>
  <input />
  <button>無法用鍵盤離開</button>
</div>
```

</div>
<div>

### ✅ React 解決方案

```jsx
// 使用原生可鍵盤操作的元素
<button onClick={handleClick}>
  點擊我
</button>

// 自定義元件加上鍵盤支援
const CustomButton = ({ onClick }) => {
  const handleKeyDown = (e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      onClick();
    }
  };

  return (
    <div
      role="button"
      tabIndex={0}
      onClick={onClick}
      onKeyDown={handleKeyDown}
    >
      按鈕
    </div>
  );
};
```

</div>
</div>

---

# 指引 2.2 - 充足時間 (Level A/AA)

提供使用者充分的時間來閱讀及使用內容

### 主要成功準則

- **2.2.1 計時調整 (A)** - 提供調整時間限制的機制
- **2.2.2 暫停、停止和隱藏 (A)** - 控制自動播放內容

<div grid="~ cols-2 gap-3">
<div>

### ❌ 常見問題

```jsx
// 沒有提供延長時間的機制
const Timer = ({ onTimeout }) => {
  useEffect(() => {
    const timer = setTimeout(onTimeout, 30000);
    return () => clearTimeout(timer);
  }, []);
  return <div>30秒後自動登出</div>;
};

// 自動播放無法控制
<video autoPlay loop>
  <source src="background.mp4" />
</video>
```

</div>
<div>

### ✅ React 解決方案

```jsx
// 提供延長時間的選項
const Timer = ({ onTimeout, onExtend }) => {
  const [showWarning, setShowWarning] = useState(false);

  return (
    <div>
      {showWarning && (
        <div role="alert">
          即將登出，是否延長時間？
          <button onClick={onExtend}>延長</button>
        </div>
      )}
    </div>
  );
};

// 可控制的自動播放
<video controls>
  <source src="content.mp4" />
</video>
```

</div>
</div>

---

# 指引 2.3 - 預防痙攣和身體不適反應 (Level A/AAA)

避免使用已知會引發痙攣或身體反應的方式來設計內容

### 主要成功準則

- **2.3.1 閃爍三次或低於閾值 (A)** - 避免快速閃爍
- **2.3.3 來自互動的動畫 (AAA)** - 可終止的動畫

<div grid="~ cols-2 gap-3">

</div>
<div>

### ❌ 避免的設計

```jsx
// 快速閃爍的動畫
.flashing {
  animation: flash 0.1s infinite;
}

@keyframes flash {
  0% { background: red; }
  50% { background: white; }
  100% { background: red; }
}

// 無法停止的動畫
const FlashingButton = () => (
  <button className="flashing">點擊我</button>
);
```

</div>

---

# 指引 2.3 - 預防痙攣和身體不適反應 (Level A/AAA)

避免使用已知會引發痙攣或身體反應的方式來設計內容
<div>

### ✅ 安全的設計

```jsx
// 尊重使用者的動畫偏好
.gentle-animation {
  animation: gentle-pulse 2s ease-in-out infinite;
}

@media (prefers-reduced-motion: reduce) {
  .gentle-animation { animation: none; }
}

// 可控制的動畫
const AnimatedButton = () => {
  const [animate, setAnimate] = useState(true);

  return (
    <button
      className={animate ? 'gentle-animation' : ''}
      onClick={() => setAnimate(!animate)}
    >
      {animate ? '停止動畫' : '開始動畫'}
    </button>
  );
};
```

</div>

# 指引 2.4 - 可導覽 (Level A/AA)

提供協助使用者導覽、尋找內容的方法

<div grid="~ cols-2 gap-4">
<div>

## ❌ 常見問題

```jsx
// 沒有跳過連結
<div>
  <nav>很長的導覽選單...</nav>
  <main>主要內容</main>
</div>

// 模糊的連結文字
<a href="/article/123">點擊這裡</a>

// 沒有頁面標題
<div>
  <h2>內容標題</h2>
</div>

// 焦點指示不清楚
.button:focus {
  outline: none; /* 移除焦點指示 */
}
```

</div>
<div>

## ✅ React 解決方案

```jsx
// 提供跳過連結
const AccessKey = ({ title, accessKey }) => (
  <a
    href={`#${accessKey}`}
    className="skip-link"
    accessKey={accessKey}
  >
    {title}
  </a>
);

// 明確的連結文字
<a href="/article/123">
  閱讀完整文章：React 無障礙開發指南
</a>

// 動態設定頁面標題
useEffect(() => {
  document.title = `${pageTitle} - 網站名稱`;
}, [pageTitle]);

// 清晰的焦點指示
.button:focus {
  outline: 2px solid #0066cc;
  outline-offset: 2px;
}
```

</div>
</div>

---

# 🧭 定位點(導盲磚)與快捷鍵實作

提供視障使用者快速導覽的重要功能

## 什麼是定位點？

定位點（Access Key）是網頁中的快速導覽功能，讓使用者可以透過鍵盤快捷鍵直接跳到網頁的重要區域，就像實體世界的導盲磚一樣。

<div grid="~ cols-2 gap-3">
<div>

## 🎯 標準定位點設定

根據台灣政府網站無障礙規範：

- **Alt + U** - 上方功能區塊
- **Alt + C** - 主要內容區塊
- **Alt + Z** - 下方功能區塊
- **Alt + S** - 網站導覽
- **Alt + R** - 相關連結

## ⌨️ 實作方式

```jsx
// 定位點元件
const AccessKey = ({ accessKey, href, children }) => (
  <a
    href={href}
    accessKey={accessKey}
    className="skip-link"
    title={`快捷鍵 Alt+${accessKey.toUpperCase()}`}
  >
    {children}
  </a>
);
```

</div>
<div>

## 🚀 React 完整實作

```jsx
const AccessibleLayout = () => (
  <div>
    {/* 跳過連結 - 隱藏但可用鍵盤存取 */}
    <div className="skip-links">
      <AccessKey accessKey="c" href="#main">
        跳到主要內容
      </AccessKey>
      <AccessKey accessKey="s" href="#nav">
        跳到網站導覽
      </AccessKey>
    </div>

    {/* 上方功能區 */}
    <header id="header">
      <AccessKey accessKey="u" href="#header">
        上方功能區塊
      </AccessKey>
      {/* 標題、搜尋等 */}
    </header>

    {/* 主要導覽 */}
    <nav id="nav" aria-label="主要導覽">
      <AccessKey accessKey="s" href="#nav">
        網站導覽
      </AccessKey>
      {/* 導覽選單 */}
    </nav>

    {/* 主要內容 */}
    <main id="main">
      <AccessKey accessKey="c" href="#main">
        主要內容區塊
      </AccessKey>
      {/* 頁面內容 */}
    </main>

    {/* 下方功能區 */}
    <footer id="footer">
      <AccessKey accessKey="z" href="#footer">
        下方功能區塊
      </AccessKey>
      {/* 頁尾資訊 */}
    </footer>
  </div>
);
```

</div>
</div>

---

# 🧭 定位點(導盲磚)與快捷鍵實作

### 🚀 React 完整實作

<div grid="~ cols-2 gap-3">
```jsx
const AccessibleLayout = () => (
  <div>
    {/* 跳過連結 - 隱藏但可用鍵盤存取 */}
    <div className="skip-links">
      <AccessKey accessKey="c" href="#main">
        跳到主要內容
      </AccessKey>
      <AccessKey accessKey="s" href="#nav">
        跳到網站導覽
      </AccessKey>
    </div>

    {/* 上方功能區 */}
    <header id="header">
      <AccessKey accessKey="u" href="#header">
        上方功能區塊
      </AccessKey>
      {/* 標題、搜尋等 */}
    </header>

    {/* 主要導覽 */}
    <nav id="nav" aria-label="主要導覽">

```
```jsx
          <AccessKey accessKey="s" href="#nav">
        網站導覽
      </AccessKey>
      {/* 導覽選單 */}
    </nav>

    {/* 主要內容 */}
    <main id="main">
      <AccessKey accessKey="c" href="#main">
        主要內容區塊
      </AccessKey>
      {/* 頁面內容 */}
    </main>

    {/* 下方功能區 */}
    <footer id="footer">
      <AccessKey accessKey="z" href="#footer">
        下方功能區塊
      </AccessKey>
      {/* 頁尾資訊 */}
    </footer>
  </div>
);
```

</div>

---

# 🎨 定位點樣式設計

<div grid="~ cols-2 gap-3">
<div>

```css
/* 跳過連結樣式 */
.skip-links {
  position: absolute;
  top: -40px;
  left: 6px;
  z-index: 1000;
}

.skip-link {
  position: absolute;
  left: -10000px;
  top: auto;
  width: 1px;
  height: 1px;
  overflow: hidden;
  background: #000;
  color: #fff;
  padding: 8px 16px;
  text-decoration: none;
  border-radius: 4px;
  font-weight: bold;
}
```

</div>
<div>
```css
/*當獲得焦點時顯示*/
.skip-link:focus {
  position: static;
  left: auto;
  width: auto;
  height: auto;
  overflow: visible;
  outline: 2px solid #fff;
  outline-offset: 2px;
}

/*定位點目標區域*/

# main:target

# nav:target

# header:target

# footer:target {

  outline: 2px solid #0066cc;
  outline-offset: 2px;
}

```
</div>
</div> 


## 💡 最佳實務

<v-clicks>

- **一定要測試**：使用 Tab 鍵確認跳過連結可以正常運作
- **提供視覺回饋**：當使用者跳到目標區域時，提供明確的視覺指示
- **語義化標記**：使用正確的 HTML 標籤（header, nav, main, footer）
- **螢幕報讀器友善**：確保跳過連結的文字描述清楚明確

</v-clicks>

---

# 指引 2.5 - 輸入方式 (Level A/AAA)

讓使用者能以鍵盤以外的各種輸入方式輕鬆操作

## 主要成功準則

- **2.5.1 指標手勢 (A)** - 提供單一指標操作替代方案
- **2.5.2 指標取消 (A)** - 提供取消或復原機制
- **2.5.3 標籤名稱 (A)** - 可見標籤與程式化名稱一致
- **2.5.4 動作啟動 (A)** - 提供替代 UI 元件操作
---

# 指引 2.5 - 輸入方式 (Level A/AAA)

<div grid="~ cols-2 gap-4">
<div>

## ❌ 常見問題

```jsx
// 只能用複雜手勢操作
const SwipeCard = () => {
  const handleSwipe = (direction) => {
    // 只能透過滑動操作
  };

  return (
    <div onTouchMove={handleSwipe}>
      滑動來操作
    </div>
  );
};

// 標籤與程式化名稱不一致
<button aria-label="關閉視窗">
  確定
</button>
```

</div>
<div>

## ✅ React 解決方案

```jsx
// 提供多種操作方式
const AccessibleCard = () => {
  const handleAction = (action) => {
    // 處理動作
  };

  return (
    <div>
      <div onTouchMove={handleSwipe}>
        滑動來操作
      </div>
      <div>
        <button onClick={() => handleAction('left')}>
          向左
        </button>
        <button onClick={() => handleAction('right')}>
          向右
        </button>
      </div>
    </div>
  );
};

// 標籤一致性
<button aria-label="確定">
  確定
</button>
```

</div>
</div>

---

# 指引 3.1 - 可讀性 (Level A/AA/AAA)

讓文字內容可讀並可理解

## 主要成功準則

- **3.1.1 網頁語言 (A)** - 指定網頁語言
- **3.1.2 局部語言 (AA)** - 標示不同語言的內容
- **3.1.4 縮寫 (AAA)** - 提供縮寫的完整形式

---

# 指引 3.1 - 可讀性 (Level A/AA/AAA)

<div grid="~ cols-2 gap-4">
<div>

## ❌ 常見問題

```jsx
// HTML 沒有設定語言
<html>
  <head>
    <title>網站標題</title>
  </head>
</html>

// 混合語言沒有標示
<p>
  這是中文內容，但是 This is English content.
</p>

// 縮寫沒有說明
<p>WHO 發布了新的指引</p>
```

</div>
<div>

## ✅ React 解決方案

```jsx
// 正確設定語言
<html lang="zh-TW">
  <head>
    <title>網站標題</title>
  </head>
</html>

// 標示不同語言
<p>
  這是中文內容，但是
  <span lang="en">This is English content.</span>
</p>

<p>
  <abbr title="世界衛生組織">WHO</abbr> 發布了新的指引
</p>

const MultilingualContent = ({ content, lang }) => (
  <div lang={lang}>
    {content}
  </div>
);
```

</div>
</div>
---

# 指引 3.2 - 可預期性 (Level A/AA)

讓網頁以可預期的方式來呈現及運作

## 主要成功準則

- **3.2.1 焦點 (A)** - 焦點變動不改變脈絡
- **3.2.2 輸入 (A)** - 輸入變動不自動改變脈絡
- **3.2.3 一致的導覽 (AA)** - 導覽機制一致
- **3.2.4 一致的識別 (AA)** - 元件識別一致

---

# 指引 3.2 - 可預期性 (Level A/AA)

<div grid="~ cols-2 gap-4">
<div>

### ❌ 常見問題

```jsx
// 焦點時自動提交
<input
  onFocus={() => submitForm()}
  placeholder="搜尋..."
/>

// 選擇時自動導航
<select onChange={(e) => {
  window.location.href = e.target.value;
}}>
  <option value="/page1">頁面1</option>
  <option value="/page2">頁面2</option>
</select>

// 不一致的導覽
const Header1 = () => (
  <nav><a href="/">首頁</a><a href="/about">關於</a></nav>
);
const Header2 = () => (
  <nav><a href="/about">關於</a><a href="/">首頁</a></nav>
);
```

</div>
</div>

---

# 指引 3.2 - 可預期性 (Level A/AA)

### ✅ React 解決方案

<div grid="~ cols-2 gap-4">

<div>

```jsx
// 不在焦點時自動執行動作
<input
  onBlur={handleSearch}
  placeholder="搜尋..."
/>
// 提供明確的提交按鈕
const NavigationSelect = () => {
  const [selected, setSelected] = useState('');
  return (
    <div>
      <select
        value={selected}
        onChange={(e) => setSelected(e.target.value)}
      >
        <option value="/page1">頁面1</option>
        <option value="/page2">頁面2</option>
      </select>
      <button onClick={() => navigate(selected)}>
        前往
      </button>
    </div>
  );
};
```

</div>
<div>

```jsx
// 一致的導覽
const Header = () => (
  <nav>
    <a href="/">首頁</a>
    <a href="/about">關於</a>
    <a href="/contact">聯絡</a>
  </nav>
);
```

</div>
</div>

---

# 指引 3.3 - 輸入協助 (Level A/AA)

幫助使用者避開及更正錯誤

<div class="bg-yellow-100 border-l-4 border-yellow-500 p-3 mb-3 text-sm">
⚠️ <strong>檢測發現問題</strong><br>
<code>HM1130104C</code> 表單控制元件未有對應的標籤組件
</div>
---

# 指引 3.3 - 輸入協助 (Level A/AA)

<div grid="~ cols-2 gap-4">
<div>
  
## ❌ 常見問題

```jsx
// 沒有標籤的輸入欄位
<input placeholder="請輸入姓名" />

// 錯誤訊息不明確
{error && <div>錯誤</div>}

// 錯誤訊息沒有關聯
<input id="email" />
<div>請輸入有效的電子郵件</div>
```

</div>

<div>

## ✅ React 解決方案

```jsx
// 完整的表單元件
<div>
  <label htmlFor="email">
    電子郵件 <span aria-label="必填">*</span>
  </label>
  <input
    id="email"
    type="email"
    aria-invalid={hasError ? 'true' : 'false'}
    aria-describedby="email-help email-error"
    required
  />
  <div id="email-help">我們會使用此電子郵件回覆您</div>
  {hasError && (
    <div id="email-error" role="alert">
      請輸入有效的電子郵件格式
    </div>
  )}
</div>
```

</div>
</div>

---

# 指引 4.1 - 相容性 (Level A/AA)

最大化與輔助科技的相容性

<div class="bg-yellow-100 border-l-4 border-yellow-500 p-4 mb-4">
⚠️ <strong>檢測發現問題</strong><br>
<code>HM1410200C</code> 未依據規格使用表單控制組件及鏈結組件<br>
<strong>影響指引：</strong> 4.1.2 名稱、角色和值 (Level A)
</div>
---

# 指引 4.1 - 相容性 (Level A/AA)

<div grid="~ cols-2 gap-4">
<div>

### ❌ 常見問題

```jsx
// 無效的 HTML 結構
<div>
  <p>段落
    <div>巢狀 div</div>
  </p>
</div>

// 缺少 ARIA 屬性
const CustomCheckbox = ({ checked, onChange }) => (
  <div onClick={onChange}>
    {checked ? '✓' : '○'}
  </div>
);

// 按鈕沒有可辨識的名稱
<button></button>

// 狀態變更沒有通知
const [message, setMessage] = useState('');
```

</div>
</div>

---

# 指引 4.1 - 相容性 (Level A/AA)

### ✅ React 解決方案

<div grid="~ cols-2 gap-4">

<div>

```jsx
// 有效的 HTML 結構
<div>
  <p>段落內容</p>
  <div>獨立的 div</div>
</div>

// 完整的 ARIA 支援
const CustomCheckbox = ({ checked, onChange, children }) => (
  <div
    role="checkbox"
    aria-checked={checked}
    tabIndex={0}
    onClick={onChange}
    onKeyDown={(e) => {
      if (e.key === ' ') {
        e.preventDefault();
        onChange();
      }
    }}
  >
    <span aria-hidden="true">{checked ? '✓' : '○'}</span>
    {children}
  </div>

```

</div>
<div>
```jsx
);

// 按鈕有明確的名稱
<button aria-label="關閉對話框">×</button>

// Live Region 通知狀態變更
<div aria-live="polite" aria-atomic="true">
  {message}
</div>
```
</div>
</div>

---

# 📊 您的專案檢測報告總結

## 發現的問題與解決建議

<div grid="~ cols-2 gap-4">
<div>

### HM1130100C

**標頭組件巢狀層次結構錯誤**

```jsx
// ✅ 正確的標題結構
<h1>頁面主標題</h1>
<h2>章節標題</h2>
<h3>子章節標題</h3>
```

### HM1130104C

**表單控制元件缺少標籤**

```jsx
// ✅ 正確的表單標籤
<label htmlFor="name">姓名</label>
<input id="name" type="text" />
```

</div>
<div>

### CS2140401C

**CSS 未使用相對字型尺寸單位**

```css
/* ✅ 使用相對單位 */
.text { font-size: 1rem; }
```

### HM1410200C

**表單控制組件規格不符**

```jsx
// ✅ 正確的 ARIA 屬性
<div role="button"
     aria-pressed="false"
     tabIndex="0">
```

</div>
</div>

---

# 🔧 Freego 無障礙檢測工具設定

## 政府認證的無障礙檢測工具

<v-clicks>

- **Freego** 是台灣政府認證的無障礙檢測工具
- 需要 **Java 21** 版本才能正常運行
- 提供完整的 WCAG 2.1 檢測報告
- 支援批次檢測和詳細錯誤分析

</v-clicks>

---

# Java 21 環境設定

## 下載與安裝

<v-clicks>

**下載 Java 21：**
[Java Archive Downloads - Java SE 21](https://www.oracle.com/java/technologies/javase/jdk21-archive-downloads.html)

**重要步驟：**

1. **移除舊版 Java 路徑**

   ```
   從系統變數 Path 中移除：
   C:\Program Files\Common Files\Oracle\Java\javapath
   ```

2. **設定 Java 21 環境變數**

   ```
   JAVA_HOME = C:\Program Files\Java\jdk-21
   Path 新增 = %JAVA_HOME%\bin
   ```

3. **移除 Java 23 版本**
   - 如果執行 Freego 時 Java 版本仍顯示 23，需移除 JDK 23

</v-clicks>

---

# 🔧 Freego Chrome Driver 設定

**問題：** 執行 Freego 檢測時瀏覽器自動關掉

<v-clicks>

**解決方案：**

1. **更新 Chrome 到最新版**

2. **下載對應的 ChromeDriver**
   - 下載連結：[Chrome for Testing](https://googlechromelabs.github.io/chrome-for-testing/#stable)
   - 直接下載：[ChromeDriver 137.0.7151.68](https://storage.googleapis.com/chrome-for-testing-public/137.0.7151.68/win64/chromedriver-win64.zip)

3. **替換 ChromeDriver**
   - 解壓縮下載的檔案
   - 替換 Freego 目錄中的 chromedriver.exe

</v-clicks>

<div class="mt-1 p-4 bg-blue-50 border-l-4 border-blue-500">
💡 <strong>提示：</strong>定期檢查 Chrome 版本並更新對應的 ChromeDriver
</div>

---

# ✅ 手動測試檢查清單

<div grid="~ cols-3 gap-3">
<div>

## 🎯 鍵盤測試

- [ ] Tab 鍵導覽所有互動元素
- [ ] 焦點指示清晰可見
- [ ] Enter/Space 啟動按鈕
- [ ] Esc 關閉對話框

</div>
<div>

## 👁️ 視覺測試

- [ ] 文字對比度足夠 (4.5:1)
- [ ] 放大到 200% 仍可使用
- [ ] 不僅依賴顏色傳達資訊
- [ ] 動畫可以暫停或關閉

</div>
<div>

## 🔊 螢幕報讀器測試

- [ ] 圖片有適當的 alt 文字
- [ ] 標題結構正確 (h1-h6)
- [ ] 表單標籤正確關聯
- [ ] 錯誤訊息會被讀出

</div>
</div>

---

# 📚 延伸學習資源

<div grid="~ cols-2 gap-4">
<div>

## 官方文件與指南

- [WCAG 2.1 Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [WAI-ARIA Authoring Practices](https://www.w3.org/WAI/ARIA/apg/)
- [React Accessibility](https://reactjs.org/docs/accessibility.html)
- [MDN Accessibility](https://developer.mozilla.org/en-US/docs/Web/Accessibility)

## 台灣相關資源

- [國家通訊傳播委員會無障礙網頁規範](https://www.ncc.gov.tw/)
- [Freego 檢測工具](https://www.freego.gov.tw/)
- [無障礙網路空間服務網](https://www.handicap-free.nat.gov.tw/)

</div>
<div>

## 實用工具

### 瀏覽器擴充

- axe DevTools
- WAVE Evaluation Tool
- Colour Contrast Analyser
- HeadingsMap
- WCAG Color contrast checker

### 線上工具

- WebAIM Contrast Checker
- Colour Contrast Analyser
- WAVE Web Accessibility Evaluator
- Pa11y Command Line Tool
- Color review

</div>
</div>

---

# 📚 延伸學習資源

<div grid="~ cols-2 gap-4">
<div>

### 螢幕報讀器

- NVDA (Windows, 免費)
- JAWS (Windows)
- VoiceOver (macOS/iOS)
- TalkBack (Android)

### Tab鍵跳轉測試

- Firefox console 輔助功能 顯示Tab鍵跳轉順序

</div>
</div>

---

# 🎉 總結

## 今天我們學到了什麼？

<v-clicks>

1. **無障礙不是選項，是必需品**
   - 法規要求、商業價值、技術品質

2. **WCAG 2.1 四大原則 (POUR)**
   - 可感知、可操作、可理解、穩健

3. **實際的程式碼解決方案**
   - 每個指引都有具體的 React 實作方法

4. **檢測工具的正確使用**
   - Freego 設定、自動化測試、手動檢查

</v-clicks>
---

# 🙋‍♂️ Q&A

## 有任何問題嗎？

<div class="text-center pt-12">
  <div class="text-6xl mb-8">🤝</div>
  <div class="text-2xl mb-4">感謝您的參與！</div>
  <div class="text-lg text-gray-600">
    讓我們一起讓網路世界變得更加無障礙
  </div>
</div>

<style>
.slidev-layout {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}
</style>
---
